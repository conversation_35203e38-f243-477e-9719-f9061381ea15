-- Create kids table to store questionnaire responses
CREATE TABLE public.kids (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  job TEXT NOT NULL,
  about TEXT NOT NULL,
  colors TEXT[] NOT NULL DEFAULT '{}',
  photo_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create generated_pages table to store rendered HTML and sharing tokens
CREATE TABLE public.generated_pages (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  kid_id UUID NOT NULL REFERENCES public.kids(id) ON DELETE CASCADE,
  share_token TEXT NOT NULL UNIQUE DEFAULT encode(gen_random_bytes(32), 'base64url'),
  html_content TEXT NOT NULL,
  generated_character_image_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create photo_uploads table for managing uploaded photos
CREATE TABLE public.photo_uploads (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  kid_id UUID NOT NULL REFERENCES public.kids(id) ON DELETE CASCADE,
  file_path TEXT NOT NULL,
  file_url TEXT NOT NULL,
  upload_status TEXT NOT NULL DEFAULT 'pending' CHECK (upload_status IN ('pending', 'completed', 'failed')),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.kids ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.generated_pages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.photo_uploads ENABLE ROW LEVEL SECURITY;

-- RLS Policies for kids table
-- Allow anyone to insert (create new kid profile)
CREATE POLICY "Anyone can create kid profiles" 
ON public.kids 
FOR INSERT 
WITH CHECK (true);

-- Allow reading only through generated_pages sharing mechanism
CREATE POLICY "Kids data only accessible through generated pages" 
ON public.kids 
FOR SELECT 
USING (false); -- Direct access denied, only through generated_pages

-- RLS Policies for generated_pages table
-- Allow anyone to insert generated pages
CREATE POLICY "Anyone can create generated pages" 
ON public.generated_pages 
FOR INSERT 
WITH CHECK (true);

-- Allow reading by share_token only (for public sharing)
CREATE POLICY "Generated pages accessible by share_token" 
ON public.generated_pages 
FOR SELECT 
USING (true); -- Will be filtered by share_token in application logic

-- RLS Policies for photo_uploads table
-- Allow anyone to insert photos
CREATE POLICY "Anyone can upload photos" 
ON public.photo_uploads 
FOR INSERT 
WITH CHECK (true);

-- Allow reading photos only when associated with accessible generated pages
CREATE POLICY "Photos accessible through generated pages" 
ON public.photo_uploads 
FOR SELECT 
USING (EXISTS (
  SELECT 1 FROM public.generated_pages 
  WHERE generated_pages.kid_id = photo_uploads.kid_id
));

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SET search_path = public;

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_kids_updated_at
  BEFORE UPDATE ON public.kids
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_generated_pages_updated_at
  BEFORE UPDATE ON public.generated_pages
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_photo_uploads_updated_at
  BEFORE UPDATE ON public.photo_uploads
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX idx_generated_pages_share_token ON public.generated_pages(share_token);
CREATE INDEX idx_generated_pages_kid_id ON public.generated_pages(kid_id);
CREATE INDEX idx_photo_uploads_kid_id ON public.photo_uploads(kid_id);

-- Create storage bucket for photos
INSERT INTO storage.buckets (id, name, public) VALUES ('kid-photos', 'kid-photos', true);

-- Create storage policies for kid photos
CREATE POLICY "Anyone can upload kid photos" 
ON storage.objects 
FOR INSERT 
WITH CHECK (bucket_id = 'kid-photos');

CREATE POLICY "Kid photos are publicly accessible" 
ON storage.objects 
FOR SELECT 
USING (bucket_id = 'kid-photos');