interface ProgressBarProps {
  currentStep: number;
  totalSteps: number;
}

export const ProgressBar = ({ currentStep, totalSteps }: ProgressBarProps) => {
  const progress = (currentStep / totalSteps) * 100;

  return (
    <div className="w-full max-w-md mx-auto mb-8">
      <div className="flex justify-between text-sm font-baloo font-semibold text-primary mb-2">
        <span>Step {currentStep}</span>
        <span>{totalSteps} Questions</span>
      </div>
      <div className="w-full bg-white/50 rounded-full h-3 shadow-inner">
        <div
          className="bg-gradient-to-r from-primary to-secondary h-3 rounded-full transition-all duration-500 ease-out shadow-sm"
          style={{ width: `${progress}%` }}
        ></div>
      </div>
    </div>
  );
};