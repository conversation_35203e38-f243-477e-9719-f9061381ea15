import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { QuestionCard } from "@/components/QuestionCard";
import { VoiceInput } from "@/components/VoiceInput";

const jobOptions = [
  { name: "Doctor", emoji: "👩‍⚕️", color: "kidPrimary" },
  { name: "Teacher", emoji: "👩‍🏫", color: "kidSecondary" },
  { name: "Engineer", emoji: "👩‍💻", color: "kidAccent" },
  { name: "Artist", emoji: "👩‍🎨", color: "kidSunshine" },
  { name: "Scientist", emoji: "👩‍🔬", color: "kidPurple" },
  { name: "Police Officer", emoji: "👮‍♀️", color: "kidPrimary" },
  { name: "Firefighter", emoji: "👩‍🚒", color: "kidSecondary" },
  { name: "Chef", emoji: "👩‍🍳", color: "kidAccent" },
  { name: "Pilot", emoji: "👩‍✈️", color: "kidSunshine" },
  { name: "<PERSON>ian", emoji: "👩‍🎤", color: "kidPurple" },
];

interface JobQuestionProps {
  value: string;
  onChange: (value: string) => void;
}

export const JobQuestion = ({ value, onChange }: JobQuestionProps) => {
  const [showCustomInput, setShowCustomInput] = useState(false);
  const [customJob, setCustomJob] = useState("");

  const handleJobSelect = (job: string) => {
    onChange(job);
    setShowCustomInput(false);
    setCustomJob("");
  };

  const handleCustomSubmit = () => {
    if (customJob.trim()) {
      onChange(customJob.trim());
    }
  };

  const handleOtherClick = () => {
    setShowCustomInput(true);
  };

  return (
    <QuestionCard className="max-w-4xl mx-auto">
      <div className="text-center space-y-8">
        <div className="space-y-2">
          <h2 className="text-3xl font-bold text-primary font-baloo">
            What do you want to be when you grow up? 🌟
          </h2>
          <p className="text-lg text-muted-foreground">
            Pick your dream job or tell us something else!
          </p>
        </div>

        {!showCustomInput ? (
          <>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
              {jobOptions.map((job) => (
                <Button
                  key={job.name}
                  variant={job.color as any}
                  size="kidDefault"
                  onClick={() => handleJobSelect(job.name)}
                  className={`flex flex-col gap-2 h-20 ${
                    value === job.name ? 'ring-4 ring-white scale-105' : ''
                  }`}
                >
                  <span className="text-2xl">{job.emoji}</span>
                  <span className="text-sm font-bold">{job.name}</span>
                </Button>
              ))}
            </div>

            <Button
              variant="outline"
              size="kidDefault"
              onClick={handleOtherClick}
              className="border-2 border-dashed border-primary/50 hover:border-primary"
            >
              ✨ Something else amazing!
            </Button>
          </>
        ) : (
          <div className="space-y-6 max-w-md mx-auto">
            <div className="space-y-4">
              <Input
                type="text"
                value={customJob}
                onChange={(e) => setCustomJob(e.target.value)}
                placeholder="What's your dream job?"
                className="text-lg h-14 rounded-xl border-2 border-primary/20 focus:border-primary text-center font-baloo font-semibold"
                onKeyPress={(e) => e.key === 'Enter' && handleCustomSubmit()}
              />
              
              <div className="flex flex-col items-center space-y-2">
                <p className="text-sm text-muted-foreground">Or say it out loud:</p>
                <VoiceInput
                  onTranscript={(transcript) => {
                    setCustomJob(transcript);
                    setTimeout(() => onChange(transcript), 500);
                  }}
                />
              </div>
            </div>

            <div className="flex gap-3 justify-center">
              <Button
                variant="kidPrimary"
                size="kidDefault"
                onClick={handleCustomSubmit}
                disabled={!customJob.trim()}
              >
                That's my dream! ✨
              </Button>
              <Button
                variant="outline"
                size="kidDefault"
                onClick={() => setShowCustomInput(false)}
              >
                Back to options
              </Button>
            </div>
          </div>
        )}

        {value && !showCustomInput && (
          <div className="bg-primary/10 rounded-xl p-4 border border-primary/20">
            <p className="text-lg font-semibold text-primary">
              Amazing choice! {value} is going to be perfect for you! 🎉
            </p>
          </div>
        )}
      </div>
    </QuestionCard>
  );
};