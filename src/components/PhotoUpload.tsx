import { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Camera, Upload, X } from "lucide-react";

interface PhotoUploadProps {
  onPhotoSelect: (file: File | null) => void;
  selectedPhoto: File | null;
}

export const PhotoUpload = ({ onPhotoSelect, selectedPhoto }: PhotoUploadProps) => {
  const [preview, setPreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (file: File) => {
    onPhotoSelect(file);
    
    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const removePhoto = () => {
    onPhotoSelect(null);
    setPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="flex flex-col items-center space-y-4">
      {preview ? (
        <div className="relative">
          <img
            src={preview}
            alt="Selected photo"
            className="w-32 h-32 rounded-full object-cover border-4 border-white shadow-xl"
          />
          <Button
            type="button"
            variant="destructive"
            size="icon"
            className="absolute -top-2 -right-2 rounded-full w-8 h-8"
            onClick={removePhoto}
          >
            <X size={16} />
          </Button>
        </div>
      ) : (
        <div className="w-32 h-32 rounded-full border-4 border-dashed border-primary/50 flex items-center justify-center bg-primary/10">
          <Camera size={40} className="text-primary/50" />
        </div>
      )}

      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileInputChange}
        className="hidden"
      />

      <Button
        type="button"
        variant="kidPrimary"
        size="kidDefault"
        onClick={() => fileInputRef.current?.click()}
        className="gap-3"
      >
        <Upload size={20} />
        {preview ? "Change Photo" : "Add Your Photo"}
      </Button>

      <p className="text-sm text-muted-foreground text-center max-w-xs">
        {preview ? "Looking great! 📸" : "Upload a photo of yourself (optional)"}
      </p>
    </div>
  );
};