import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { name, job, about, colors, photoFile } = await req.json();
    
    console.log('Received data:', { name, job, about, colors, hasPhoto: !!photoFile });

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    const supabaseKey = Deno.env.get("SUPABASE_ANON_KEY");
    
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Handle photo upload if provided
    let photoUrl = null;
    if (photoFile) {
      const photoData = Uint8Array.from(atob(photoFile.split(',')[1]), c => c.charCodeAt(0));
      const fileName = `${crypto.randomUUID()}.jpg`;
      
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('kid-photos')
        .upload(fileName, photoData, {
          contentType: 'image/jpeg',
          upsert: false
        });

      if (uploadError) {
        console.error('Photo upload error:', uploadError);
      } else {
        const { data: { publicUrl } } = supabase.storage
          .from('kid-photos')
          .getPublicUrl(fileName);
        photoUrl = publicUrl;
        console.log('Photo uploaded:', photoUrl);
      }
    }

    // Create kid record
    const { data: kidData, error: kidError } = await supabase
      .from('kids')
      .insert({
        name,
        job,
        about,
        colors,
        photo_url: photoUrl
      })
      .select()
      .single();

    if (kidError) {
      console.error('Kid creation error:', kidError);
      throw new Error('Failed to create kid profile');
    }

    console.log('Kid created:', kidData.id);

    // Generate HTML with Gemini API
    const geminiApiKey = Deno.env.get('GEMINI_API_KEY');
    if (!geminiApiKey) {
      throw new Error('Gemini API key not configured');
    }

    const systemPrompt = `Generate a static, responsive HTML web page for a child using the provided data. If a face photo URL is provided, generate inspirational character images (e.g., doctor, engineer, teacher, police) that incorporate the child's face. Use the supplied color palette to produce a colorful, cute design appropriate for kids. Output the final HTML as a single, self-contained static page.`;

    const userPrompt = `Create a fun, inspiring landing page for ${name} who wants to be a ${job} when they grow up. 

About them: ${about}

Favorite colors: ${colors.join(', ')}

${photoUrl ? `Photo URL: ${photoUrl}` : 'No photo provided'}

Make it super colorful, kid-friendly, and inspiring. Include fun animations and playful fonts. The page should celebrate their dreams and make them excited about their future!`;

    const geminiResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${geminiApiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: `${systemPrompt}\n\n${userPrompt}`
          }]
        }],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 8192,
        }
      }),
    });

    if (!geminiResponse.ok) {
      console.error('Gemini API error:', await geminiResponse.text());
      throw new Error('Failed to generate content');
    }

    const geminiData = await geminiResponse.json();
    const htmlContent = geminiData.candidates[0].content.parts[0].text;

    console.log('HTML generated, length:', htmlContent.length);

    // Save generated page
    const { data: pageData, error: pageError } = await supabase
      .from('generated_pages')
      .insert({
        kid_id: kidData.id,
        html_content: htmlContent,
        generated_character_image_url: null // Could be used for AI-generated character images later
      })
      .select()
      .single();

    if (pageError) {
      console.error('Page creation error:', pageError);
      throw new Error('Failed to save generated page');
    }

    console.log('Page created with share_token:', pageData.share_token);

    return new Response(
      JSON.stringify({ 
        success: true,
        shareToken: pageData.share_token,
        shareUrl: `https://eoynzadwiebjuvkfcwep.supabase.co/functions/v1/share-page/${pageData.share_token}`
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    );

  } catch (error) {
    console.error('Error in generate-page function:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Failed to generate page', 
        details: error.message 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    );
  }
});