import { useState } from "react";
import { WelcomeScreen } from "@/components/WelcomeScreen";
import { Questionnaire } from "@/components/Questionnaire";

interface FormData {
  name: string;
  job: string;
  about: string;
  colors: string[];
  photo: File | null;
}

const Index = () => {
  const [currentScreen, setCurrentScreen] = useState<'welcome' | 'questionnaire' | 'result'>('welcome');
  const [formData, setFormData] = useState<FormData | null>(null);

  const handleStart = () => {
    setCurrentScreen('questionnaire');
  };

  const handleQuestionnaireComplete = (data: FormData) => {
    setFormData(data);
    setCurrentScreen('result');
    // TODO: Generate landing page and handle API calls
    console.log('Form completed:', data);
  };

  if (currentScreen === 'welcome') {
    return <WelcomeScreen onStart={handleStart} />;
  }

  if (currentScreen === 'questionnaire') {
    return <Questionnaire onComplete={handleQuestionnaireComplete} />;
  }

  // Result screen (temporary)
  return (
    <div className="min-h-screen bg-rainbow flex items-center justify-center px-4">
      <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-8 max-w-2xl mx-auto border-2 border-white/50">
        <div className="text-center space-y-6">
          <div className="text-6xl mb-4">🎉</div>
          <h1 className="text-3xl font-bold text-primary font-baloo">
            Amazing! Your page is being created!
          </h1>
          <p className="text-lg text-muted-foreground">
            We're building something special just for you, {formData?.name}!
          </p>
          <div className="bg-primary/10 rounded-xl p-4 text-left space-y-2">
            <p><strong>Name:</strong> {formData?.name}</p>
            <p><strong>Future Job:</strong> {formData?.job}</p>
            <p><strong>About You:</strong> {formData?.about}</p>
            <p><strong>Favorite Colors:</strong> {formData?.colors.length} selected</p>
            <p><strong>Photo:</strong> {formData?.photo ? 'Added ✓' : 'Not added'}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
