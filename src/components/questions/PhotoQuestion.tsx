import { QuestionCard } from "@/components/QuestionCard";
import { PhotoUpload } from "@/components/PhotoUpload";

interface PhotoQuestionProps {
  value: File | null;
  onChange: (photo: File | null) => void;
}

export const PhotoQuestion = ({ value, onChange }: PhotoQuestionProps) => {
  return (
    <QuestionCard className="max-w-lg mx-auto">
      <div className="text-center space-y-6">
        <div className="space-y-2">
          <h2 className="text-3xl font-bold text-primary font-baloo">
            Add your awesome photo! 📸
          </h2>
          <p className="text-lg text-muted-foreground">
            Show the world your amazing smile! (This step is optional)
          </p>
        </div>

        <PhotoUpload
          onPhotoSelect={onChange}
          selectedPhoto={value}
        />

        <div className="bg-sunshine/10 rounded-lg p-4">
          <p className="text-sm text-muted-foreground">
            🌟 <strong>Optional step:</strong> You can skip this if you prefer 
            and still create an amazing page about your future!
          </p>
        </div>
      </div>
    </QuestionCard>
  );
};