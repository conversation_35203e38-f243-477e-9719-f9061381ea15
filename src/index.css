@tailwind base;
@tailwind components;
@tailwind utilities;

/* Kid-friendly design system with vibrant colors and playful elements */

@layer base {
  :root {
    /* Base colors - soft and welcoming */
    --background: 45 100% 96%;
    --foreground: 240 15% 15%;

    /* Card system - clean and bright */
    --card: 0 0% 100%;
    --card-foreground: 240 15% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 15% 15%;

    /* Primary palette - electric blue */
    --primary: 210 100% 55%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 210 100% 70%;

    /* Secondary palette - hot pink */
    --secondary: 320 100% 70%;
    --secondary-foreground: 0 0% 100%;

    /* Accent colors - lime green */
    --accent: 120 100% 50%;
    --accent-foreground: 240 15% 15%;

    /* Sunshine yellow */
    --sunshine: 50 100% 60%;
    --sunshine-foreground: 240 15% 15%;

    /* Purple magic */
    --purple: 280 100% 70%;
    --purple-foreground: 0 0% 100%;

    /* Muted for backgrounds */
    --muted: 45 50% 92%;
    --muted-foreground: 240 10% 45%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 20% 85%;
    --input: 0 0% 100%;
    --ring: 210 100% 55%;

    --radius: 1rem;

    /* Kid-friendly gradients */
    --gradient-rainbow: linear-gradient(135deg, 
      hsl(210 100% 55%) 0%, 
      hsl(320 100% 70%) 25%, 
      hsl(50 100% 60%) 50%, 
      hsl(120 100% 50%) 75%, 
      hsl(280 100% 70%) 100%);
    
    --gradient-sky: linear-gradient(135deg, 
      hsl(210 100% 70%) 0%, 
      hsl(200 100% 80%) 100%);
    
    --gradient-sunset: linear-gradient(135deg, 
      hsl(320 100% 70%) 0%, 
      hsl(50 100% 60%) 100%);

    /* Shadows with color */
    --shadow-colorful: 0 8px 32px -8px hsl(210 100% 55% / 0.3);
    --shadow-pink: 0 8px 32px -8px hsl(320 100% 70% / 0.3);
    --shadow-green: 0 8px 32px -8px hsl(120 100% 50% / 0.3);
    
    /* Animations */
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-baloo;
  }
}

@layer components {
  /* Kid-friendly button animations */
  .btn-bounce {
    @apply transition-all duration-300 ease-out hover:scale-105 active:scale-95;
    box-shadow: var(--shadow-colorful);
  }
  
  .btn-bounce:hover {
    transform: translateY(-2px) scale(1.05);
  }
  
  /* Floating animation for decorative elements */
  .float {
    animation: float 3s ease-in-out infinite;
  }
  
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }
  
  /* Gradient backgrounds */
  .bg-rainbow {
    background: var(--gradient-rainbow);
  }
  
  .bg-sky {
    background: var(--gradient-sky);
  }
  
  .bg-sunset {
    background: var(--gradient-sunset);
  }
  
  /* Voice recording pulse */
  .voice-pulse {
    animation: pulse-ring 1.5s ease-out infinite;
  }
  
  @keyframes pulse-ring {
    0% {
      transform: scale(0.33);
      opacity: 1;
    }
    80%, 100% {
      transform: scale(2.33);
      opacity: 0;
    }
  }
  
  /* Card hover effects */
  .card-hover {
    @apply transition-all duration-300 hover:scale-105;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }
  
  .card-hover:hover {
    box-shadow: var(--shadow-colorful);
  }
}
