import { useState } from "react";
import { Button } from "@/components/ui/button";
import { ProgressBar } from "@/components/ProgressBar";
import { NameQuestion } from "@/components/questions/NameQuestion";
import { JobQuestion } from "@/components/questions/JobQuestion";
import { AboutQuestion } from "@/components/questions/AboutQuestion";
import { ColorsQuestion } from "@/components/questions/ColorsQuestion";
import { PhotoQuestion } from "@/components/questions/PhotoQuestion";
import { ChevronLeft, ChevronRight, Sparkles } from "lucide-react";

interface FormData {
  name: string;
  job: string;
  about: string;
  colors: string[];
  photo: File | null;
}

interface QuestionnaireProps {
  onComplete: (data: FormData) => void;
}

export const Questionnaire = ({ onComplete }: QuestionnaireProps) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<FormData>({
    name: "",
    job: "",
    about: "",
    colors: [],
    photo: null,
  });

  const totalSteps = 5;

  const updateField = <K extends keyof FormData>(field: K, value: FormData[K]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const canProceed = () => {
    switch (currentStep) {
      case 1: return formData.name.trim().length > 0;
      case 2: return formData.job.trim().length > 0;
      case 3: return formData.about.trim().length > 0;
      case 4: return formData.colors.length > 0;
      case 5: return true; // Photo is optional
      default: return false;
    }
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(prev => prev + 1);
    } else {
      onComplete(formData);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const renderQuestion = () => {
    switch (currentStep) {
      case 1:
        return (
          <NameQuestion
            value={formData.name}
            onChange={(value) => updateField('name', value)}
          />
        );
      case 2:
        return (
          <JobQuestion
            value={formData.job}
            onChange={(value) => updateField('job', value)}
          />
        );
      case 3:
        return (
          <AboutQuestion
            value={formData.about}
            onChange={(value) => updateField('about', value)}
          />
        );
      case 4:
        return (
          <ColorsQuestion
            value={formData.colors}
            onChange={(value) => updateField('colors', value)}
          />
        );
      case 5:
        return (
          <PhotoQuestion
            value={formData.photo}
            onChange={(value) => updateField('photo', value)}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-rainbow flex flex-col">
      <div className="container mx-auto px-4 py-8 flex-1">
        <ProgressBar currentStep={currentStep} totalSteps={totalSteps} />
        
        <div className="flex-1 flex items-center justify-center">
          {renderQuestion()}
        </div>

        <div className="flex justify-between items-center mt-8 max-w-lg mx-auto">
          <Button
            variant="outline"
            size="kidDefault"
            onClick={prevStep}
            disabled={currentStep === 1}
            className="gap-2"
          >
            <ChevronLeft size={20} />
            Back
          </Button>

          <div className="text-center">
            <p className="text-sm font-semibold text-white/80">
              {currentStep} of {totalSteps}
            </p>
          </div>

          <Button
            variant="kidPrimary"
            size="kidDefault"
            onClick={nextStep}
            disabled={!canProceed()}
            className="gap-2"
          >
            {currentStep === totalSteps ? (
              <>
                <Sparkles size={20} />
                Create My Page!
              </>
            ) : (
              <>
                Next
                <ChevronRight size={20} />
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};