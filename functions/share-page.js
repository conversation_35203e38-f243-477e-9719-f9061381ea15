const { createClient } = require('@supabase/supabase-js');

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, OPTIONS'
};

exports.handler = async (event, context) => {
  // Handle CORS preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  // Only allow GET requests
  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    // Extract share token from query parameters
    const shareToken = event.queryStringParameters?.token;

    if (!shareToken) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: 'Share token required'
      };
    }

    console.log('Looking up share token:', shareToken);

    // Initialize Supabase client
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Supabase configuration missing');
    }

    const supabase = createClient(supabaseUrl, supabaseKey);

    // Fetch the generated page by share token
    const { data: pageData, error } = await supabase
      .from('generated_pages')
      .select('html_content')
      .eq('share_token', shareToken)
      .maybeSingle();

    if (error) {
      console.error('Database error:', error);
      return {
        statusCode: 500,
        headers: corsHeaders,
        body: 'Database error'
      };
    }

    if (!pageData) {
      return {
        statusCode: 404,
        headers: corsHeaders,
        body: 'Page not found'
      };
    }

    console.log('Serving HTML page for token:', shareToken);

    // Return the HTML content directly
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'text/html',
        'Cache-Control': 'public, max-age=3600',
        ...corsHeaders
      },
      body: pageData.html_content
    };

  } catch (error) {
    console.error('Error in share-page function:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: 'Internal server error'
    };
  }
};