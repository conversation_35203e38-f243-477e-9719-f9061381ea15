const { createClient } = require('@supabase/supabase-js');

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};

exports.handler = async (event, context) => {
  // Handle CORS preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    const { name, job, about, colors, photoFile } = JSON.parse(event.body);

    console.log('Received data:', { name, job, about, colors, hasPhoto: !!photoFile });

    // Initialize Supabase client
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Supabase configuration missing');
    }

    const supabase = createClient(supabaseUrl, supabaseKey);

    // Handle photo upload if provided
    let photoUrl = null;
    if (photoFile) {
      try {
        // Extract base64 data from data URL
        const base64Data = photoFile.split(',')[1];
        const photoData = Buffer.from(base64Data, 'base64');
        const fileName = `${Date.now()}-${Math.random().toString(36).substring(7)}.jpg`;

        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('kid-photos')
          .upload(fileName, photoData, {
            contentType: 'image/jpeg',
            upsert: false
          });

        if (uploadError) {
          console.error('Photo upload error:', uploadError);
        } else {
          const { data: { publicUrl } } = supabase.storage
            .from('kid-photos')
            .getPublicUrl(fileName);
          photoUrl = publicUrl;
          console.log('Photo uploaded:', photoUrl);
        }
      } catch (photoError) {
        console.error('Photo processing error:', photoError);
        // Continue without photo if upload fails
      }
    }

    // Create kid record
    const { data: kidData, error: kidError } = await supabase
      .from('kids')
      .insert({
        name,
        job,
        about,
        colors,
        photo_url: photoUrl
      })
      .select()
      .single();

    if (kidError) {
      console.error('Kid creation error:', kidError);
      throw new Error('Failed to create kid profile');
    }

    console.log('Kid created:', kidData.id);

    // Generate HTML with Gemini API
    const geminiApiKey = process.env.GEMINI_API_KEY;
    if (!geminiApiKey) {
      throw new Error('Gemini API key not configured');
    }

    const systemPrompt = `Generate a static, responsive HTML web page for a child using the provided data. If a face photo URL is provided, generate inspirational character images (e.g., doctor, engineer, teacher, police) that incorporate the child's face. Use the supplied color palette to produce a colorful, cute design appropriate for kids. Output the final HTML as a single, self-contained static page.`;

    const userPrompt = `Create a fun, inspiring landing page for ${name} who wants to be a ${job} when they grow up.

About them: ${about}

Favorite colors: ${colors.join(', ')}

${photoUrl ? `Photo URL: ${photoUrl}` : 'No photo provided'}

Make it super colorful, kid-friendly, and inspiring. Include fun animations and playful fonts. The page should celebrate their dreams and make them excited about their future! Use the Baloo font from Google Fonts for a playful look.

Requirements:
- Self-contained HTML with inline CSS
- Responsive design that works on mobile
- Use the child's favorite colors as the theme
- Include social media meta tags for sharing
- Add a text-to-speech button to read the story aloud
- Make it visually appealing with animations and kid-friendly design`;
console.log(userPrompt);

    const geminiResponse = await fetch(`${process.env.GEMINI_API_URL}/${process.env.GEMINI_API_MODEL}:generateContent?key=${geminiApiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          role: "user",
          parts: [{
            text: `${systemPrompt}\n\n${userPrompt}`
          }]
        }],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 8192,
        }
      }),
    });

    if (!geminiResponse.ok) {
      const errorData = await geminiResponse.json().catch(() => ({ error: geminiResponse.statusText || 'Unknown error' }));
      console.error('Gemini API error:', errorData);
      throw new Error('Failed to generate content');
    }

    const geminiData = await geminiResponse.json();

    if (!geminiData.candidates || geminiData.candidates.length === 0 || !geminiData.candidates[0].content || !geminiData.candidates[0].content.parts || geminiData.candidates[0].content.parts.length === 0) {
      throw new Error('No content found in Gemini API response');
    }

    const htmlContent = geminiData.candidates[0].content.parts[0].text;

    console.log('HTML generated, length:', htmlContent.length);

    // Save generated page
    const { data: pageData, error: pageError } = await supabase
      .from('generated_pages')
      .insert({
        kid_id: kidData.id,
        html_content: htmlContent,
        generated_character_image_url: null // Could be used for AI-generated character images later
      })
      .select()
      .single();

    if (pageError) {
      console.error('Page creation error:', pageError);
      throw new Error('Failed to save generated page');
    }

    console.log('Page created with share_token:', pageData.share_token);

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        shareToken: pageData.share_token,
        shareUrl: `${process.env.URL || 'http://localhost:8888'}/.netlify/functions/share-page?token=${pageData.share_token}`
      })
    };

  } catch (error) {
    console.error('Error in generate-page function:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({
        error: 'Failed to generate page',
        details: error.message
      })
    };
  }
};



