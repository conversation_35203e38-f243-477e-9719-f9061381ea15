import { useState } from "react";
import { WelcomeScreen } from "@/components/WelcomeScreen";
import { Questionnaire } from "@/components/Questionnaire";
import { ResultScreen } from "@/components/ResultScreen";

interface FormData {
  name: string;
  job: string;
  about: string;
  colors: string[];
  photo: File | null;
}

const Index = () => {
  const [currentScreen, setCurrentScreen] = useState<'welcome' | 'questionnaire' | 'result'>('welcome');
  const [formData, setFormData] = useState<FormData | null>(null);

  const handleStart = () => {
    setCurrentScreen('questionnaire');
  };

  const handleQuestionnaireComplete = (data: FormData) => {
    setFormData(data);
    setCurrentScreen('result');
  };

  const handleBackToWelcome = () => {
    setCurrentScreen('welcome');
    setFormData(null);
  };

  if (currentScreen === 'welcome') {
    return <WelcomeScreen onStart={handleStart} />;
  }

  if (currentScreen === 'questionnaire') {
    return <Questionnaire onComplete={handleQuestionnaireComplete} />;
  }

  // Result screen with page generation
  if (formData) {
    return <ResultScreen formData={formData} onBack={handleBackToWelcome} />;
  }

  return null;
};

export default Index;
