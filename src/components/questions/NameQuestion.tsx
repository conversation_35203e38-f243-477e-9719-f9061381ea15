import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { QuestionCard } from "@/components/QuestionCard";
import { VoiceInput } from "@/components/VoiceInput";

interface NameQuestionProps {
  value: string;
  onChange: (value: string) => void;
}

export const NameQuestion = ({ value, onChange }: NameQuestionProps) => {
  return (
    <QuestionCard className="max-w-lg mx-auto">
      <div className="text-center space-y-6">
        <div className="space-y-2">
          <h2 className="text-3xl font-bold text-primary font-baloo">
            What's your awesome name? ✨
          </h2>
          <p className="text-lg text-muted-foreground">
            Tell us what everyone calls you!
          </p>
        </div>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name" className="text-lg font-semibold">
              Your Name
            </Label>
            <Input
              id="name"
              type="text"
              value={value}
              onChange={(e) => onChange(e.target.value)}
              placeholder="Type your name here..."
              className="text-lg h-14 rounded-xl border-2 border-primary/20 focus:border-primary text-center font-baloo font-semibold"
            />
          </div>

          <div className="flex flex-col items-center space-y-2">
            <p className="text-sm text-muted-foreground">Or say it out loud:</p>
            <VoiceInput
              onTranscript={(transcript) => onChange(transcript)}
            />
          </div>
        </div>
      </div>
    </QuestionCard>
  );
};