import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import heroImage from "@/assets/hero-kids-future.jpg";

interface WelcomeScreenProps {
  onStart: () => void;
}

export const WelcomeScreen = ({ onStart }: WelcomeScreenProps) => {
  return (
    <div className="min-h-screen bg-rainbow flex items-center justify-center px-4">
      <Card className="max-w-2xl mx-auto p-8 card-hover bg-white/90 backdrop-blur-sm border-2 border-white/50">
        <div className="text-center space-y-8">
          {/* Hero Section */}
          <div className="space-y-6">
            <div className="relative">
              <img 
                src={heroImage} 
                alt="Kids dreaming about their future careers"
                className="w-full h-48 object-cover rounded-2xl shadow-xl"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-primary/20 to-transparent rounded-2xl"></div>
            </div>
            
            <div className="flex justify-center space-x-2 text-4xl">
              <span className="float">🌟</span>
              <span className="float" style={{ animationDelay: '0.5s' }}>✨</span>
              <span className="float" style={{ animationDelay: '1s' }}>🚀</span>
            </div>
            
            <h1 className="text-4xl md:text-5xl font-bold text-primary font-baloo bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Future Me Builder
            </h1>
            
            <p className="text-xl text-muted-foreground font-baloo">
              Create an awesome page about the amazing person you'll become!
            </p>
          </div>

          {/* Features */}
          <div className="grid md:grid-cols-3 gap-6">
            <div className="space-y-3">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                <Sparkles className="w-8 h-8 text-primary" />
              </div>
              <h3 className="font-bold text-lg text-primary font-baloo">Dream Big</h3>
              <p className="text-sm text-muted-foreground">
                Tell us about your future career and what makes you special!
              </p>
            </div>

            <div className="space-y-3">
              <div className="w-16 h-16 bg-secondary/10 rounded-full flex items-center justify-center mx-auto">
                <Heart className="w-8 h-8 text-secondary" />
              </div>
              <h3 className="font-bold text-lg text-secondary font-baloo">Make It Yours</h3>
              <p className="text-sm text-muted-foreground">
                Pick your favorite colors and add your photo to personalize it!
              </p>
            </div>

            <div className="space-y-3">
              <div className="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mx-auto">
                <Rocket className="w-8 h-8 text-accent" />
              </div>
              <h3 className="font-bold text-lg text-accent font-baloo">Share & Inspire</h3>
              <p className="text-sm text-muted-foreground">
                Get a beautiful page you can share with family and friends!
              </p>
            </div>
          </div>

          {/* Call to Action */}
          <div className="space-y-4">
            <Button
              variant="kidPrimary"
              size="kidLarge"
              onClick={onStart}
              className="text-xl px-12 gap-3"
            >
              <Sparkles size={24} />
              Start Building My Future!
            </Button>
            
            <p className="text-sm text-muted-foreground">
              Just 5 fun questions and you're done! 🎉
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
};