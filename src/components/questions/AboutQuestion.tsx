import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { QuestionCard } from "@/components/QuestionCard";
import { VoiceInput } from "@/components/VoiceInput";

interface AboutQuestionProps {
  value: string;
  onChange: (value: string) => void;
}

export const AboutQuestion = ({ value, onChange }: AboutQuestionProps) => {
  return (
    <QuestionCard className="max-w-lg mx-auto">
      <div className="text-center space-y-6">
        <div className="space-y-2">
          <h2 className="text-3xl font-bold text-primary font-baloo">
            Tell us about yourself! 🌈
          </h2>
          <p className="text-lg text-muted-foreground">
            What makes you special and unique?
          </p>
        </div>

        <div className="space-y-4">
          <div className="space-y-2 text-left">
            <Label htmlFor="about" className="text-lg font-semibold">
              About You
            </Label>
            <Textarea
              id="about"
              value={value}
              onChange={(e) => onChange(e.target.value)}
              placeholder="I love playing soccer, drawing pictures, and helping my friends..."
              className="min-h-32 text-lg rounded-xl border-2 border-primary/20 focus:border-primary font-baloo resize-none"
            />
          </div>

          <div className="flex flex-col items-center space-y-2">
            <p className="text-sm text-muted-foreground">Or tell us with your voice:</p>
            <VoiceInput
              onTranscript={(transcript) => onChange(value + " " + transcript)}
            />
          </div>

          <div className="bg-accent/10 rounded-lg p-4 text-left">
            <p className="text-sm text-muted-foreground">
              💡 <strong>Ideas to share:</strong> Your hobbies, favorite subjects, 
              what you're good at, or what makes you happy!
            </p>
          </div>
        </div>
      </div>
    </QuestionCard>
  );
};