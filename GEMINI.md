## Kid Inspiration — Product Documentation

Overview
- A vibrant, child-friendly full‑stack web app that creates a personalized, inspirational landing page showing each child’s future self 10 years from now. The app guides kids through an easy, friendly questionnaire (one page at a time), then generates a colorful static page (HTML) with optional photo-driven character art, TTS audio, and a shareable URL.

---
## Key Features
- Multi-step, Typeform-like questionnaire (one question per screen).
- Text and voice (dictation) inputs for name, story, and occupation.
- Large tappable selectable occupation options with an “Other” field.
- Color picker: choose 2–3 favorite colors using large swatches.
- Optional front‑facing portrait upload with preview.
- Server-side generation using Gemini API to produce a self-contained responsive HTML landing page that:
  - Displays the kid’s photo (if provided) and story.
  - Prominently shows the chosen dream occupation.
  - Uses the selected colors to create a colorful, kid-friendly theme.
  - Uses playful readable fonts (e.g., Baloo).
  - Optionally generates inspirational character images incorporating the face photo.
- TTS audio button to play the kid’s story.
- Stored rendered HTML and asset URLs for fast sharing via a unique shareable URL (share_token).
- Mobile‑first design, large interactions, subtle animations.

---
## User Flow (Detailed)
1. Landing / Intro screen: short friendly intro, “Start” button.
2. Sequential questions (one-per-screen):
   - Name — text input + microphone icon for dictation.
   - Dream job — grid of clickable large option cards (doctor, engineer, teacher, police, volunteer, model, etc.) + “Other” with text input; microphone available.
   - Short story — multi-line text or voice dictation.
   - Favorite colors — choose 2–3 swatches from a bright palette (visual swatches, selectable; show selection order or removal).
   - Optional photo — upload front-facing portrait with immediate preview; show guidance on framing and privacy.
3. Review screen: allow edits to any answer. 
    - Keep expire time for each generation or permenant - for permanent, there will be a form to request deleting
4. Submit: client sends collected data and assets to backend.
5. Backend:
   - Stores photo to object storage and returns URL.
   - Calls Gemini with a system prompt to generate a responsive static HTML page (see System Prompt below).
   - Optionally calls image API (when kid provide their face photo) to generate inspiration character with their  face; stores image URLs.
   - Optionally calls TTS API to generate an audio file of the story; stores audio URL.
   - Stores final HTML, asset URLs, metadata, and a secure share_token in the database.
6. Client receives the shareable URL and displays the generated page in-app; share link opens the static page directly. ( There won't be authentication for the whole app, so keep it unique and secure for every kid. )
7. There will be a button to share the page on social media.
8. There will be a button to sending a request form for deleting the page.

---
## Gemini System Prompt (use exactly or adapt)
Generate a static, responsive HTML web page for a child using the provided data. If a face photo URL is provided, generate inspirational character images (e.g., doctor, engineer, teacher, police) that incorporate the child's face. Use the supplied color palette to produce a colorful, cute design appropriate for kids. Output the final HTML as a single, self-contained static page.

---
## Design & UX Guidelines
- Colors: bright blues, pinks, greens, yellows, purples; high contrast for readability.
- Typography: playful and readable (Baloo, Comic-style fallbacks). Keep font sizes large for children.
- Controls: large buttons and inputs, generous spacing, clear affordances.
- Color swatches: large circular or rounded-square swatches; visible check/number for selection order.
- Photo uploader: camera and file options; preview and ability to retake/remove.
- Accessibility: large touch targets, readable contrast, simple language, clear labels.
- Animations: gentle micro-interactions (fade, slide, bounce) to increase joy without distraction.

---
## Data Model (Supabase/Postgres)
Check this supabase type file `src/integrations/supabase/types.ts`


Security:
- Public share endpoint returns only the stored HTML for the token.
- Rate-limit generation to prevent abuse.

---
## Backend Responsibilities
- Photo handling: accept uploads, validate size/type, optionally auto-crop/center face, store to object storage (Supabase Storage, S3).
- Gemini API:
  - Use the system prompt above.
  - Pass structured data (name, story, occupation, colors, photo_url).
  - Expect a single file HTML output (self-contained: inline CSS, base64 images if needed).
- Google Banana Image API.
  - Generate inspirational character images; store and return image_urls.
- TTS (optional)
  - Generate audio of the kid’s story; store and return audio_url.
  - Offer small audio file (ogg/mp3).
- Storage:
  - Persist final HTML and asset URLs.
  - Generate and persist share_token.
- Logging:
  - Keep generation logs for debugging and analytics (do not store sensitive PII beyond necessary).

Privacy & Safety
- Make face photo optional.
- Store personal data only as required; no public exposure except on shared pages.
- Use unguessable share tokens.
- Offer delete/expire functionality for pages on request.
- Consider parental consent flow if required by law (depending on region).

---
## Frontend Implementation (React + Next.js + Tailwind)
- Multi-step form component that:
  - Presents one question per route/state.
  - Uses react-speech-recognition (or equivalent) for mic/dictation controls.
  - Large option cards for occupations; “Other” reveals a text input.
  - Color picker implemented as selectable swatches (limit to 2–3).
  - File input + camera access for photo with preview and cropping helper.
- Review & submit screen showing collected answers.
- After generation: show final page preview inside an iframe or redirect to /p/{share_token}.
- Generated page:
  - Use responsive layout, inline styles/fonts, TTS play button bound to audio_url.
  - If audio not available, fallback to on-page Web Speech Synthesis playback.

---
## Generated Page Requirements
- Self-contained, responsive HTML.
- Prominently display: kid’s name, dream occupation, story text, and photo if provided.
- Theme driven by selected colors.
- Playful font + accessible sizing.
- TTS play button using the generated audio file (or the Web Speech API fallback).
- Include metadata for social sharing (og:title, og:description, og:image if available).

---
## Example Implementation Notes
- Fonts: load Baloo from Google Fonts, fallback system fonts.
- Color algorithm: pick primary from first selected color, secondary from second, accent from third. Use CSS variables to apply theme.
- Photo-driven characters: if using a 3rd-party image API or model, send face_url and request character variants; otherwise use stylized overlays/frames using the photo.
- Security: serve /p/{share_token} through a static-render route that only loads stored HTML.

---
## Deployment & Ops
- Deploy frontend and serverless functions to Vercel (Next.js).
- Use Supabase for Postgres + Storage (photos + generated audio/html blobs).
- Environment variables: GEMINI_API_KEY, TTS_API_KEY, DATABASE_URL, STORAGE credentials.
- CI/CD: auto-deploy on main branch; protect generation endpoints with rate limits and monitoring.
- Backup: periodic database backups; retention policy for generated pages.

---
## Deliverables
- Full React/Next.js frontend source with:
  - Multi-step question form
  - Voice dictation integration
  - Color swatches and photo uploader
  - Preview and share UI
- Backend serverless functions:
  - Photo upload handler
  - Gemini generation function (system prompt included)
  - TTS audio generation handler
  - Persistence to Supabase
- Tailwind configuration and styles tuned for kid-friendly color palette and fonts.
- SQL schema for Supabase (tables above).
- Deployment guide (Vercel + Supabase setup, env vars, build steps).
- Optional: sample Gemini request/response and sample generated HTML.

---
## Next Steps (Recommended)
- I can produce:
  - SQL schema for Supabase tables.
  - Example Next.js API function templates for photo upload, Gemini call, and TTS.
  - Example Gemini request payload and the exact system prompt packaged for use.
  - Starter React components for the multi-step form and color picker.

Which of the above would you like first?