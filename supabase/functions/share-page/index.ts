import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const url = new URL(req.url);
    const shareToken = url.pathname.split('/').pop();

    if (!shareToken) {
      return new Response('Share token required', { status: 400 });
    }

    console.log('Looking up share token:', shareToken);

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    const supabaseKey = Deno.env.get("SUPABASE_ANON_KEY");
    
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Fetch the generated page by share token
    const { data: pageData, error } = await supabase
      .from('generated_pages')
      .select('html_content')
      .eq('share_token', shareToken)
      .maybeSingle();

    if (error) {
      console.error('Database error:', error);
      return new Response('Database error', { status: 500 });
    }

    if (!pageData) {
      return new Response('Page not found', { status: 404 });
    }

    console.log('Serving HTML page for token:', shareToken);

    // Return the HTML content directly
    return new Response(pageData.html_content, {
      headers: {
        'Content-Type': 'text/html',
        'Cache-Control': 'public, max-age=3600',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('Error in share-page function:', error);
    return new Response('Internal server error', { status: 500 });
  }
});