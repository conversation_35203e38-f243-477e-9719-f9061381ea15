import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap font-baloo font-semibold ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90 btn-bounce",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80 btn-bounce",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        // Kid-friendly variants
        kidPrimary: "bg-primary text-primary-foreground hover:bg-primary-glow rounded-2xl text-lg btn-bounce shadow-lg",
        kidSecondary: "bg-secondary text-secondary-foreground hover:bg-secondary/90 rounded-2xl text-lg btn-bounce shadow-lg",
        kidAccent: "bg-accent text-accent-foreground hover:bg-accent/90 rounded-2xl text-lg btn-bounce shadow-lg",
        kidSunshine: "bg-sunshine text-sunshine-foreground hover:bg-sunshine/90 rounded-2xl text-lg btn-bounce shadow-lg",
        kidPurple: "bg-purple text-purple-foreground hover:bg-purple/90 rounded-2xl text-lg btn-bounce shadow-lg",
        voice: "bg-gradient-to-r from-primary to-secondary text-white rounded-full btn-bounce shadow-xl",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
        // Kid-friendly sizes
        kidDefault: "h-14 px-8 py-4 text-lg",
        kidLarge: "h-16 px-10 py-5 text-xl",
        kidIcon: "h-14 w-14",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return <Comp className={cn(buttonVariants({ variant, size, className }))} ref={ref} {...props} />;
  },
);
Button.displayName = "Button";

export { Button, buttonVariants };
