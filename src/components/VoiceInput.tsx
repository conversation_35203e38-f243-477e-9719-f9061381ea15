import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Mic, MicO<PERSON> } from "lucide-react";
import { useSpeechRecognition } from "react-speech-kit";

interface VoiceInputProps {
  onTranscript: (transcript: string) => void;
  disabled?: boolean;
}

export const VoiceInput = ({ onTranscript, disabled = false }: VoiceInputProps) => {
  const [isListening, setIsListening] = useState(false);
  
  const { listen, stop, supported } = useSpeechRecognition({
    onResult: (result) => {
      onTranscript(result);
      setIsListening(false);
    },
    onEnd: () => setIsListening(false),
  });

  const handleVoiceToggle = () => {
    if (isListening) {
      stop();
      setIsListening(false);
    } else {
      listen();
      setIsListening(true);
    }
  };

  if (!supported) {
    return null;
  }

  return (
    <div className="relative">
      <Button
        type="button"
        variant="voice"
        size="kidIcon"
        onClick={handleVoiceToggle}
        disabled={disabled}
        className={`relative ${isListening ? 'animate-pulse' : ''}`}
      >
        {isListening ? <MicOff size={24} /> : <Mic size={24} />}
        {isListening && (
          <div className="absolute inset-0 rounded-full voice-pulse bg-primary/30"></div>
        )}
      </Button>
      {isListening && (
        <p className="text-sm text-center mt-2 text-primary font-semibold animate-pulse">
          Listening... 🎤
        </p>
      )}
    </div>
  );
};