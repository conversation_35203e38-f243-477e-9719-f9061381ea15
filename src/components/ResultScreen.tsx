import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { generatePage } from "@/services/api";
import { Loader2, ExternalLink, Share2, RefreshCw } from "lucide-react";

interface FormData {
  name: string;
  job: string;
  about: string;
  colors: string[];
  photo: File | null;
}

interface ResultScreenProps {
  formData: FormData;
  onBack: () => void;
}

export const ResultScreen = ({ formData, onBack }: ResultScreenProps) => {
  const [isGenerating, setIsGenerating] = useState(true);
  const [shareUrl, setShareUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const generateUserPage = async () => {
      try {
        setIsGenerating(true);
        setError(null);
        
        console.log('Starting page generation for:', formData.name);
        const result = await generatePage(formData);
        
        setShareUrl(result.shareUrl);
        console.log('Page generated successfully:', result.shareUrl);
      } catch (err) {
        console.error('Failed to generate page:', err);
        setError(err instanceof Error ? err.message : 'Failed to generate your page');
      } finally {
        setIsGenerating(false);
      }
    };

    generateUserPage();
  }, [formData]);

  const handleShare = async () => {
    if (!shareUrl) return;

    if (navigator.share) {
      try {
        await navigator.share({
          title: `${formData.name}'s Future Dreams`,
          text: `Check out ${formData.name}'s amazing future as a ${formData.job}!`,
          url: shareUrl,
        });
      } catch (err) {
        console.log('Error sharing:', err);
        // Fallback to copying to clipboard
        copyToClipboard();
      }
    } else {
      copyToClipboard();
    }
  };

  const copyToClipboard = () => {
    if (shareUrl) {
      navigator.clipboard.writeText(shareUrl);
      // You could add a toast notification here
      alert('Link copied to clipboard!');
    }
  };

  const handleRetry = () => {
    setIsGenerating(true);
    setError(null);
    setShareUrl(null);
    
    // Retry generation
    const generateUserPage = async () => {
      try {
        const result = await generatePage(formData);
        setShareUrl(result.shareUrl);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to generate your page');
      } finally {
        setIsGenerating(false);
      }
    };

    generateUserPage();
  };

  if (isGenerating) {
    return (
      <div className="min-h-screen bg-rainbow flex items-center justify-center px-4">
        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-8 max-w-2xl mx-auto border-2 border-white/50">
          <div className="text-center space-y-6">
            <div className="flex justify-center">
              <Loader2 className="h-16 w-16 animate-spin text-primary" />
            </div>
            <h1 className="text-3xl font-bold text-primary font-baloo">
              Creating your amazing page! ✨
            </h1>
            <p className="text-lg text-muted-foreground">
              We're building something special just for you, {formData.name}!
            </p>
            <div className="bg-primary/10 rounded-xl p-4 text-left space-y-2">
              <p><strong>Name:</strong> {formData.name}</p>
              <p><strong>Future Job:</strong> {formData.job}</p>
              <p><strong>About You:</strong> {formData.about}</p>
              <p><strong>Favorite Colors:</strong> {formData.colors.join(', ')}</p>
              <p><strong>Photo:</strong> {formData.photo ? 'Added ✓' : 'Not added'}</p>
            </div>
            <p className="text-sm text-muted-foreground">
              This might take a few moments while we create your personalized page...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-rainbow flex items-center justify-center px-4">
        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-8 max-w-2xl mx-auto border-2 border-white/50">
          <div className="text-center space-y-6">
            <div className="text-6xl mb-4">😔</div>
            <h1 className="text-3xl font-bold text-destructive font-baloo">
              Oops! Something went wrong
            </h1>
            <p className="text-lg text-muted-foreground">
              We couldn't create your page right now.
            </p>
            <div className="bg-destructive/10 rounded-xl p-4">
              <p className="text-sm text-destructive">{error}</p>
            </div>
            <div className="flex gap-4 justify-center">
              <Button
                variant="kidPrimary"
                size="kidDefault"
                onClick={handleRetry}
                className="gap-2"
              >
                <RefreshCw size={20} />
                Try Again
              </Button>
              <Button
                variant="outline"
                size="kidDefault"
                onClick={onBack}
              >
                Go Back
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-rainbow flex items-center justify-center px-4">
      <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-8 max-w-2xl mx-auto border-2 border-white/50">
        <div className="text-center space-y-6">
          <div className="text-6xl mb-4">🎉</div>
          <h1 className="text-3xl font-bold text-primary font-baloo">
            Your amazing page is ready!
          </h1>
          <p className="text-lg text-muted-foreground">
            Congratulations {formData.name}! Your personalized future page has been created.
          </p>
          
          <div className="bg-primary/10 rounded-xl p-4 text-left space-y-2">
            <p><strong>Name:</strong> {formData.name}</p>
            <p><strong>Future Job:</strong> {formData.job}</p>
            <p><strong>About You:</strong> {formData.about}</p>
            <p><strong>Favorite Colors:</strong> {formData.colors.join(', ')}</p>
            <p><strong>Photo:</strong> {formData.photo ? 'Added ✓' : 'Not added'}</p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              variant="kidPrimary"
              size="kidDefault"
              onClick={() => window.open(shareUrl!, '_blank')}
              className="gap-2"
            >
              <ExternalLink size={20} />
              View Your Page
            </Button>
            <Button
              variant="kidSecondary"
              size="kidDefault"
              onClick={handleShare}
              className="gap-2"
            >
              <Share2 size={20} />
              Share with Friends
            </Button>
          </div>

          <div className="bg-sunshine/10 rounded-lg p-4">
            <p className="text-sm text-muted-foreground">
              🌟 <strong>Your page is ready to share!</strong> Send the link to family and friends 
              so they can see your amazing future dreams!
            </p>
          </div>

          <Button
            variant="outline"
            size="kidDefault"
            onClick={onBack}
          >
            Create Another Page
          </Button>
        </div>
      </div>
    </div>
  );
};
