import { useState } from "react";
import { Check } from "lucide-react";

const kidColors = [
  { name: "Electric Blue", value: "hsl(210, 100%, 55%)", bg: "bg-blue-500" },
  { name: "Hot Pink", value: "hsl(320, 100%, 70%)", bg: "bg-pink-500" },
  { name: "Lime Green", value: "hsl(120, 100%, 50%)", bg: "bg-green-500" },
  { name: "Sunshine Yellow", value: "hsl(50, 100%, 60%)", bg: "bg-yellow-400" },
  { name: "Purple Magic", value: "hsl(280, 100%, 70%)", bg: "bg-purple-500" },
  { name: "Orange Burst", value: "hsl(30, 100%, 60%)", bg: "bg-orange-500" },
  { name: "Turquoise", value: "hsl(180, 100%, 50%)", bg: "bg-cyan-500" },
  { name: "<PERSON>", value: "hsl(340, 100%, 70%)", bg: "bg-rose-500" },
  { name: "Mint", value: "hsl(160, 100%, 60%)", bg: "bg-emerald-400" },
  { name: "Lavender", value: "hsl(260, 100%, 80%)", bg: "bg-violet-400" },
];

interface ColorPickerProps {
  selectedColors: string[];
  onColorSelect: (colors: string[]) => void;
  maxColors?: number;
}

export const ColorPicker = ({ selectedColors, onColorSelect, maxColors = 3 }: ColorPickerProps) => {
  const handleColorClick = (colorValue: string) => {
    if (selectedColors.includes(colorValue)) {
      // Remove color if already selected
      onColorSelect(selectedColors.filter(c => c !== colorValue));
    } else if (selectedColors.length < maxColors) {
      // Add color if under limit
      onColorSelect([...selectedColors, colorValue]);
    }
  };

  return (
    <div className="grid grid-cols-5 gap-4">
      {kidColors.map((color) => {
        const isSelected = selectedColors.includes(color.value);
        return (
          <button
            key={color.name}
            type="button"
            onClick={() => handleColorClick(color.value)}
            className={`
              relative w-16 h-16 rounded-full transition-all duration-300 hover:scale-110 focus:outline-none focus:ring-4 focus:ring-primary/50
              ${color.bg}
              ${isSelected ? 'ring-4 ring-white shadow-xl scale-110' : 'shadow-lg'}
            `}
            title={color.name}
          >
            {isSelected && (
              <div className="absolute inset-0 flex items-center justify-center">
                <Check className="text-white w-6 h-6 drop-shadow-lg" />
              </div>
            )}
          </button>
        );
      })}
    </div>
  );
};