interface FormData {
  name: string;
  job: string;
  about: string;
  colors: string[];
  photo: File | null;
}

interface GeneratePageResponse {
  success: boolean;
  shareToken: string;
  shareUrl: string;
}

interface ApiError {
  error: string;
  details?: string;
}

// Convert File to base64 data URL
const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });
};

export const generatePage = async (formData: FormData): Promise<GeneratePageResponse> => {
  try {
    // Convert photo to base64 if provided
    let photoFile = null;
    if (formData.photo) {
      photoFile = await fileToBase64(formData.photo);
    }

    const payload = {
      name: formData.name,
      job: formData.job,
      about: formData.about,
      colors: formData.colors,
      photoFile
    };

    console.log('Sending request to generate-page:', { ...payload, photoFile: photoFile ? 'base64_data' : null });

    const response = await fetch('/.netlify/functions/generate-page', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorData: ApiError = await response.json();
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    const result: GeneratePageResponse = await response.json();
    console.log('Page generation successful:', result);
    
    return result;
  } catch (error) {
    console.error('Error generating page:', error);
    throw error;
  }
};

export const generateCharacterImage = async (photoUrl: string, job: string, name: string, colors: string[]) => {
  try {
    const payload = {
      photoUrl,
      job,
      name,
      colors
    };

    const response = await fetch('/.netlify/functions/generate-image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorData: ApiError = await response.json();
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error generating character image:', error);
    throw error;
  }
};
