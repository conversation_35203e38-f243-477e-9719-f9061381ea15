import { QuestionCard } from "@/components/QuestionCard";
import { ColorPicker } from "@/components/ColorPicker";

interface ColorsQuestionProps {
  value: string[];
  onChange: (colors: string[]) => void;
}

export const ColorsQuestion = ({ value, onChange }: ColorsQuestionProps) => {
  return (
    <QuestionCard className="max-w-2xl mx-auto">
      <div className="text-center space-y-8">
        <div className="space-y-2">
          <h2 className="text-3xl font-bold text-primary font-baloo">
            What are your favorite colors? 🎨
          </h2>
          <p className="text-lg text-muted-foreground">
            Pick up to 3 colors that make you happy!
          </p>
        </div>

        <ColorPicker
          selectedColors={value}
          onColorSelect={onChange}
          maxColors={3}
        />

        {value.length > 0 && (
          <div className="bg-gradient-to-r from-primary/10 to-secondary/10 rounded-xl p-6 border border-primary/20">
            <p className="text-lg font-semibold text-primary mb-3">
              Beautiful choices! 🌈
            </p>
            <div className="flex justify-center gap-3">
              {value.map((color, index) => (
                <div
                  key={index}
                  className="w-8 h-8 rounded-full border-2 border-white shadow-lg"
                  style={{ backgroundColor: color }}
                ></div>
              ))}
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              {value.length < 3 ? `You can pick ${3 - value.length} more!` : "Perfect selection!"}
            </p>
          </div>
        )}
      </div>
    </QuestionCard>
  );
};