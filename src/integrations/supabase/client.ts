// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://eoynzadwiebjuvkfcwep.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVveW56YWR3aWVianV2a2Zjd2VwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTcyNDIwMzgsImV4cCI6MjA3MjgxODAzOH0.UPx3R5HGtvGC9Hvep7QOYD8Uu0lCPOSUtK8_AK_2laE";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});